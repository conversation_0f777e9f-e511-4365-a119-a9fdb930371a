FileScopeMCP 功能总结
基于官方文档，FileScopeMCP 是一个强大的代码库分析工具，主要功能如下：

🎯 核心功能
1. 文件重要性分析
智能评分：基于依赖关系为每个文件分配 0-10 的重要性评分
依赖权重：考虑文件的入度（被多少文件导入）和出度（导入多少文件）
位置权重：根据文件位置（如 src/ 目录）和文件名（如 main、index）调整权重
快速定位：帮助快速识别代码库中最关键的文件
2. 依赖关系追踪
双向依赖：追踪文件间的导入和被导入关系
多语言支持：支持 Python、JavaScript/TypeScript、C/C++、Rust、Lua、Zig、C#、Java
本地vs包依赖：区分本地文件依赖和外部包依赖
依赖图谱：构建完整的项目依赖关系网络
3. 可视化图表生成
Mermaid 图表：生成专业的依赖关系图
多种样式：支持依赖图、目录树、混合视图等
颜色编码：基于重要性评分进行颜色区分
HTML 输出：支持响应式设计，包含主题切换功能
自定义选项：可调整深度、重要性阈值、布局方向等
4. 文件摘要管理
摘要存储：为任意文件添加人工或AI生成的摘要
持久化：摘要信息在服务器重启后保持
快速理解：帮助快速掌握文件用途和功能
5. 多项目支持
多文件树：为不同项目区域创建和管理多个文件树
独立配置：每个文件树有独立的基础目录配置
快速切换：在不同文件树间轻松切换
缓存机制：缓存文件树以提高后续操作速度
🔧 技术特性
存储与持久化
JSON 格式：所有数据自动保存为 JSON 格式
增量更新：支持加载现有文件树而无需重新扫描
时间戳追踪：记录文件树最后更新时间
文件监控
实时监控：可选的文件变化监控功能
自动重建：文件变化时自动重建文件树
配置灵活：可配置监控新文件、删除、修改等事件
跨平台支持
路径标准化：处理 Windows 和 Unix 路径格式
绝对/相对路径：支持各种路径格式
URL 编码：处理 URL 编码的路径
🚀 使用场景
代码库理解：快速了解新项目的核心文件和架构
重构规划：识别高影响文件，制定重构策略
依赖分析：分析模块间依赖关系，发现循环依赖
文档生成：自动生成项目结构图和依赖图
AI 辅助开发：为 AI 助手提供代码库上下文信息
🛠️ 集成方式
MCP 协议：通过 Model Context Protocol 与 Cursor 等 AI 工具集成
TypeScript 实现：基于 TypeScript/Node.js 构建
简单配置：通过 JSON 配置文件轻松集成到开发环境
FileScopeMCP 是一个专为现代开发工作流设计的智能代码分析工具，特别适合与 AI 编程助手配合使用，能够显著提升代码理解和开发效率。