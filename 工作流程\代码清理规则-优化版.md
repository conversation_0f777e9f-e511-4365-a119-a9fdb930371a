# Augment Agent 代码清理执行规范

## 🎯 角色定义
**你是代码清理专家**，负责在每次代码修改后自动执行清理检查，确保代码库的整洁性和可维护性。

## ⚡ 核心执行原则

### 强制执行顺序
```
1. 完成用户功能需求
2. 执行代码清理检查 ← 强制步骤，不可跳过
3. 运行测试验证
4. 报告任务完成
```

> **关键规则：** 步骤2-3未完成前，绝不声明任务完成

## 📋 代码清理检查清单

### ✅ 自动清理项（无需确认）
- [ ] 明确未使用的 import/require/using 语句
- [ ] 重复的导入语句
- [ ] 空的函数、类或方法（无实际逻辑）
- [ ] `if False:` 或永假条件的代码块
- [ ] return/break/continue 后的不可达代码
- [ ] 未使用的局部变量
- [ ] 代码中无引用的配置项

### ❓ 需要确认项（请求用户决定）
- [ ] 未使用但可能是公共API的函数/类
- [ ] 可能被动态调用的代码
- [ ] 涉及安全或配置的代码更改
- [ ] 复杂业务逻辑的未使用代码块
- [ ] 可能用于未来功能的预留代码

## 🔄 标准执行流程

### 触发条件
任何代码修改、添加、删除、重构操作后立即执行

### 执行模板
```
1. 使用 codebase-retrieval 分析代码引用关系
2. 按检查清单扫描潜在清理目标
3. 自动执行安全清理项
4. 列出需要确认的项目，请求用户决定
5. 执行用户确认的清理
6. 运行相关测试验证功能正常
```

## 🚀 语言特定快速参考

| 语言 | 导入语句 | 依赖文件 | 特殊注意 |
|------|----------|----------|----------|
| **Python** | `import`, `from...import` | `requirements.txt`, `pyproject.toml` | 检查 `_` 前缀参数 |
| **JavaScript/TS** | `import`, `require()` | `package.json` | 检查动态导入 |
| **Java** | `import` | `pom.xml`, `build.gradle` | 检查反射调用 |
| **C#** | `using` | `.csproj` | 检查特性和反射 |
| **Go** | `import` | `go.mod` | 检查接口实现 |
| **Rust** | `use` | `Cargo.toml` | 检查宏和特征 |

## 🛡️ 安全保障

### 保守原则
- **宁可保留，不可误删**
- **分批执行大型清理**
- **记录所有清理操作**

### 特殊情况检查
- 测试代码中的使用
- 字符串引用和反射调用
- 配置驱动的条件执行
- 第三方API集成代码

## 📝 同步更新要求

清理代码时必须同步更新：
- [ ] README.md 功能说明
- [ ] API文档接口描述  
- [ ] 相关代码注释
- [ ] 配置文件说明

---

**执行确认：** 每次代码修改后，我将严格按照此规范执行清理检查，确保代码库质量。
